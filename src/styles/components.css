/* 组件特定样式 */

/* 按钮组件样式 */
.button-variants {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.button-default {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
  @apply h-10 py-2 px-4;
}

.button-destructive {
  @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
}

.button-outline {
  @apply border border-input hover:bg-accent hover:text-accent-foreground;
}

.button-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

.button-ghost {
  @apply hover:bg-accent hover:text-accent-foreground;
}

.button-link {
  @apply underline-offset-4 hover:underline text-primary;
}

/* 输入框组件样式 */
.input {
  @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

/* 卡片组件样式 */
.card {
  @apply rounded-lg border bg-card text-card-foreground shadow-sm;
}

.card-header {
  @apply flex flex-col space-y-1.5 p-6;
}

.card-title {
  @apply text-2xl font-semibold leading-none tracking-tight;
}

.card-description {
  @apply text-sm text-muted-foreground;
}

.card-content {
  @apply p-6 pt-0;
}

.card-footer {
  @apply flex items-center p-6 pt-0;
}

/* 对话框组件样式 */
.dialog-overlay {
  @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm;
}

.dialog-content {
  @apply fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:w-full;
}

/* 导航组件样式 */
.nav-link {
  @apply text-sm font-medium transition-colors hover:text-primary;
}

.nav-link-active {
  @apply text-primary;
}

/* 表单组件样式 */
.form-label {
  @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
}

.form-description {
  @apply text-sm text-muted-foreground;
}

.form-message {
  @apply text-sm font-medium text-destructive;
}
